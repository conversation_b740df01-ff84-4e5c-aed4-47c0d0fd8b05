# 证书提取功能说明

## 功能概述

本功能从SSL协议元数据中提取原始证书数据，并将其发送到Kafka topic供certificate-analyzer模块消费和分析。

## 新增字段

在`ZMPNMsg.proto`的`ssl_msg`中新增了两个字段：

```protobuf
repeated bytes ssl_cert_c_raw = 43;  // Raw certificate data for client certificates in chain
repeated bytes ssl_cert_s_raw = 44;  // Raw certificate data for server certificates in chain
```

## 架构设计

```
SSL协议元数据 → CertificateExtractor → Kafka Topic → certificate-analyzer
```

### 处理流程

1. **数据接入**: 从Kafka接收包含SSL协议元数据的protobuf消息
2. **证书提取**: CertificateExtractor算子从ssl_cert_c_raw和ssl_cert_s_raw字段提取原始证书数据
3. **数据发送**: 将提取的证书数据发送到Kafka的certfile topic
4. **证书分析**: certificate-analyzer模块消费证书数据进行分析

## 核心组件

### 1. CertificateExtractor
- **位置**: `com.geeksec.nta.datawarehouse.etl.ods.processor.CertificateExtractor`
- **功能**: 从SSL消息中提取客户端和服务端证书链数据
- **输出**: 通过侧输出流输出原始证书字节数据

### 2. CertificateDeserializationSchema
- **位置**: `com.geeksec.certificateanalyzer.source.kafka.CertificateDeserializationSchema`
- **功能**: 将Kafka消息中的原始证书字节数据反序列化为X509Certificate对象
- **特性**: 支持DER和PEM格式，包含错误处理机制

## 配置说明

### 主要配置项

```properties
# 启用证书提取功能
certificate.extraction.enabled=true

# 证书提取并行度
certificate.extraction.parallelism=2

# 证书数据Kafka topic
kafka.topic.certificate.files=certfile
```

### 配置文件位置
- 主配置: `application.yml`
- 示例配置: `certificate-extraction.properties`

## 部署说明

### 1. 更新配置
在data-warehouse-processor的配置文件中添加：
```yaml
certificate:
  extraction:
    enabled: true
    parallelism: 2
```

### 2. 重启服务
重启data-warehouse-processor和certificate-analyzer服务以应用新功能。

### 3. 监控验证
- 检查证书提取日志
- 监控Kafka topic消息数量
- 验证certificate-analyzer消费情况

## 监控指标

- 提取的证书数量
- 证书数据大小分布
- 处理延迟
- 错误率

## 故障排查

### 常见问题

1. **证书提取未启用**
   - 检查`certificate.extraction.enabled`配置
   - 确认配置文件加载正确

2. **Kafka连接问题**
   - 验证Kafka broker地址
   - 检查topic是否存在
   - 确认网络连通性

3. **证书解析失败**
   - 检查证书数据格式
   - 查看解析错误日志
   - 验证证书数据完整性

## 性能优化

- 根据证书数据量调整并行度
- 优化Kafka生产者配置
- 监控内存使用情况
- 调整批处理大小
