package com.geeksec.nta.datawarehouse.sink;

import com.geeksec.common.core.constants.ConfigConstants;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;

import org.apache.flink.formats.json.JsonSerializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.kafka.clients.producer.ProducerConfig;

import java.util.Properties;

/**
 * Manages the configuration and creation of Kafka Sinks.
 *
 * <AUTHOR>
 */
@Slf4j
public final class KafkaSinkManager {

    private KafkaSinkManager() {
        // Utility class
    }

    /**
     * Configures and creates Kafka Sinks for various protocol data streams.
     *
     * @param sideOutStream The side output stream containing different protocol
     *                      data.
     * @param config        The application configuration.
     */
    public static void configureKafkaSinks(
            SingleOutputStreamOperator<Row> sideOutStream,
            ParameterTool config) {

        // 从配置中读取 Kafka Sink 的并行度，如果未设置则默认为2
        int kafkaSinkParallelism = config.getInt(ConfigConstants.PARALLELISM_KAFKA_JSON_SINK, 2);

        // Session Stream
        sideOutStream.getSideOutput(MessageOutputTag.SESSION_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_CONNECT)))
                .name("Kafka Sink: Session to "
                        + config.getRequired(ConfigConstants.KAFKA_TOPIC_CONNECT))
                .setParallelism(kafkaSinkParallelism);

        // HTTP Stream
        sideOutStream.getSideOutput(MessageOutputTag.HTTP_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_HTTP)))
                .name("Kafka Sink: HTTP to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_HTTP))
                .setParallelism(kafkaSinkParallelism);

        // DNS Stream
        sideOutStream.getSideOutput(MessageOutputTag.DNS_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_DNS)))
                .name("Kafka Sink: DNS to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_DNS))
                .setParallelism(kafkaSinkParallelism);

        // SSL Stream
        sideOutStream.getSideOutput(MessageOutputTag.SSL_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_SSL)))
                .name("Kafka Sink: SSL to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_SSL))
                .setParallelism(kafkaSinkParallelism);

        // SSH Stream
        sideOutStream.getSideOutput(MessageOutputTag.SSH_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_SSH)))
                .name("Kafka Sink: SSH to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_SSH))
                .setParallelism(kafkaSinkParallelism);

        // RLOGIN Stream
        sideOutStream.getSideOutput(MessageOutputTag.RLOGIN_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_RLOGIN)))
                .name("Kafka Sink: RLOGIN to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_RLOGIN))
                .setParallelism(kafkaSinkParallelism);

        // TELNET Stream
        sideOutStream.getSideOutput(MessageOutputTag.TELNET_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_TELNET)))
                .name("Kafka Sink: TELNET to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_TELNET))
                .setParallelism(kafkaSinkParallelism);

        // RDP Stream
        sideOutStream.getSideOutput(MessageOutputTag.RDP_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_RDP)))
                .name("Kafka Sink: RDP to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_RDP))
                .setParallelism(kafkaSinkParallelism);

        // VNC Stream
        sideOutStream.getSideOutput(MessageOutputTag.VNC_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_VNC)))
                .name("Kafka Sink: VNC to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_VNC))
                .setParallelism(kafkaSinkParallelism);

        // XDMCP Stream
        sideOutStream.getSideOutput(MessageOutputTag.XDMCP_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_XDMCP)))
                .name("Kafka Sink: XDMCP to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_XDMCP))
                .setParallelism(kafkaSinkParallelism);

        // NTP Stream
        sideOutStream.getSideOutput(MessageOutputTag.NTP_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_NTP)))
                .name("Kafka Sink: NTP to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_NTP))
                .setParallelism(kafkaSinkParallelism);

        // ICMP Stream
        sideOutStream.getSideOutput(MessageOutputTag.ICMP_STREAM)
                .sinkTo(buildKafkaSink(config,
                        config.getRequired(ConfigConstants.KAFKA_TOPIC_ICMP)))
                .name("Kafka Sink: ICMP to " + config.getRequired(ConfigConstants.KAFKA_TOPIC_ICMP))
                .setParallelism(kafkaSinkParallelism);
    }

    /**
     * 构建 Kafka Sink
     *
     * @param config 参数工具类
     * @param topic  目标主题
     * @return 配置好的 KafkaSink 实例
     */
    private static KafkaSink<Row> buildKafkaSink(
            ParameterTool config,
            String topic) {
        String kafkaBootstrapServers = config.getRequired(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS);
        // 获取生产者配置
        Properties producerProps = getProducerProperties(config);

        // 创建 Kafka 记录序列化 schema
        KafkaRecordSerializationSchema<Row> recordSerializer = KafkaRecordSerializationSchema.builder()
                .setTopic(topic)
                .setKeySerializationSchema(new KafkaKeySerializer())
                .setValueSerializationSchema(new JsonSerializationSchema<Row>())
                .build();
        return KafkaSink.<Row>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setKafkaProducerConfig(producerProps)
                .setRecordSerializer(recordSerializer)
                .build();
    }

    /**
     * Configures and creates a Kafka Sink for a specific data stream.
     *
     * @param stream     The data stream to sink to Kafka.
     * @param config     The application configuration.
     * @param streamType The type of the stream (e.g., "dns", "http", "ssl", "session").
     */
    public static void configureKafkaSink(DataStream<Row> stream, ParameterTool config, String streamType) {
        String topic = getKafkaTopic(config, streamType);
        int sinkParallelism = config.getInt(ConfigConstants.PARALLELISM_KAFKA_JSON_SINK, 2);

        stream.sinkTo(buildKafkaSink(config, topic))
                .name("Kafka Sink: " + streamType.toUpperCase() + " to " + topic)
                .setParallelism(sinkParallelism);
    }

    /**
     * 配置证书数据Kafka输出
     *
     * @param certificateStream 证书数据流
     * @param config           配置参数
     */
    public static void configureCertificateKafkaSink(DataStream<byte[]> certificateStream, ParameterTool config) {
        try {
            String topic = config.get(ConfigConstants.KAFKA_TOPIC_CERTIFICATE_FILES, "certfile");
            int sinkParallelism = config.getInt(ConfigConstants.CERTIFICATE_EXTRACTION_PARALLELISM, 2);

            log.info("配置证书数据Kafka输出，topic: {}, 并行度: {}", topic, sinkParallelism);

            // 创建Kafka Sink
            KafkaSink<byte[]> kafkaSink = KafkaSink.<byte[]>builder()
                    .setBootstrapServers(config.get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS))
                    .setRecordSerializer(KafkaRecordSerializationSchema.<byte[]>builder()
                            .setTopic(topic)
                            .setValueSerializationSchema(new org.apache.flink.api.common.serialization.SerializationSchema<byte[]>() {
                                @Override
                                public byte[] serialize(byte[] element) {
                                    return element; // 直接返回原始字节数据
                                }
                            })
                            .build())
                    .setProperty("retries", "3")
                    .setProperty("acks", "all")
                    .setProperty("enable.idempotence", "true")
                    .setProperty("compression.type", "snappy")
                    .build();

            // 配置输出
            certificateStream
                    .map(certBytes -> {
                        log.debug("发送证书数据，大小: {} 字节", certBytes.length);
                        return certBytes;
                    })
                    .sinkTo(kafkaSink)
                    .name("Certificate Kafka Sink")
                    .setParallelism(sinkParallelism);

            log.info("证书数据Kafka输出配置完成");

        } catch (Exception e) {
            log.error("配置证书数据Kafka输出时发生异常", e);
        }
    }

    /**
     * 获取指定流类型对应的Kafka主题
     *
     * @param config     配置参数
     * @param streamType 流类型
     * @return Kafka主题名
     */
    private static String getKafkaTopic(ParameterTool config, String streamType) {
        String topicConfigKey = "kafka.topic." + streamType.toLowerCase();
        String defaultTopic = "ods_" + streamType.toLowerCase() + "_metadata";
        return config.get(topicConfigKey, defaultTopic);
    }

    /**
     * 创建 Kafka 生产者属性
     *
     * @param config 参数工具类
     * @return 生产者属性
     */
    private static Properties getProducerProperties(ParameterTool config) {
        Properties producerProps = new Properties();

        // 设置 Kafka 生产者配置
        producerProps.setProperty(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                config.get("kafka.bootstrap.servers"));
        producerProps.setProperty(ProducerConfig.RETRIES_CONFIG, "3");
        producerProps.setProperty(ProducerConfig.ACKS_CONFIG, "all");
        producerProps.setProperty(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, "true");
        producerProps.setProperty(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, "5");
        producerProps.setProperty(ProducerConfig.COMPRESSION_TYPE_CONFIG, "snappy");

        // 添加其他配置
        producerProps.putAll(config.getProperties());

        // 添加Kafka安全配置
        String securityProtocol = config.get(ConfigConstants.KAFKA_SECURITY_PROTOCOL, "");
        if (securityProtocol != null && !securityProtocol.isEmpty()) {
            log.info("Configuring Kafka producer security with protocol: {}", securityProtocol);
            producerProps.put("security.protocol", securityProtocol);
        }

        String username = config.get(ConfigConstants.KAFKA_CLIENT_USER, "");
        String password = config.get(ConfigConstants.KAFKA_CLIENT_PASSWORD, "");

        if (username != null && !username.isEmpty() && password != null && !password.isEmpty()) {
            String jaasConfigFormat = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";";
            String jaasConfig = String.format(jaasConfigFormat, username, password);
            producerProps.put("sasl.jaas.config", jaasConfig);
            log.info("Configured SASL authentication for producer with user: {}", username);
        } else {
            log.warn("Kafka producer security credentials (username/password) not fully configured or are empty.");
        }

        return producerProps;
    }

    /**
     * Configures and creates Kafka Sinks for enriched data streams.
     * These Kafka topics will be consumed by the graph-builder component for graph
     * data construction.
     *
     * @param config                The application configuration.
     * @param enrichedSessionStream Enriched session data stream.
     * @param enrichedDnsStream     Enriched DNS data stream.
     * @param enrichedHttpStream    Enriched HTTP data stream.
     * @param enrichedSslStream     Enriched SSL data stream.
     */
    public static void configureEnrichedDataKafkaSinks(
            ParameterTool config,
            DataStream<Row> enrichedSessionStream,
            DataStream<Row> enrichedDnsStream,
            DataStream<Row> enrichedHttpStream,
            DataStream<Row> enrichedSslStream) {

        int kafkaSinkParallelism = config.getInt(ConfigConstants.PARALLELISM_KAFKA_JSON_SINK, 2);

        // Sink for enriched session data
        if (enrichedSessionStream != null) {
            String topic = config.getRequired(ConfigConstants.KAFKA_TOPIC_CONNECT);
            enrichedSessionStream
                    .sinkTo(buildKafkaSink(config, topic))
                    .name("Enriched Session to Kafka: " + topic)
                    .setParallelism(kafkaSinkParallelism);
        }

        // Sink for enriched DNS data
        if (enrichedDnsStream != null) {
            String topic = config.getRequired(ConfigConstants.KAFKA_TOPIC_DNS);
            enrichedDnsStream
                    .sinkTo(buildKafkaSink(config, topic))
                    .name("Enriched DNS to Kafka: " + topic)
                    .setParallelism(kafkaSinkParallelism);
        }

        // Sink for enriched HTTP data
        if (enrichedHttpStream != null) {
            String topic = config.getRequired(ConfigConstants.KAFKA_TOPIC_HTTP);
            enrichedHttpStream
                    .sinkTo(buildKafkaSink(config, topic))
                    .name("Enriched HTTP to Kafka: " + topic)
                    .setParallelism(kafkaSinkParallelism);
        }

        // Sink for enriched SSL data
        if (enrichedSslStream != null) {
            String topic = config.getRequired(ConfigConstants.KAFKA_TOPIC_SSL);
            enrichedSslStream
                    .sinkTo(buildKafkaSink(config, topic))
                    .name("Enriched SSL to Kafka: " + topic)
                    .setParallelism(kafkaSinkParallelism);
        }
    }
}
