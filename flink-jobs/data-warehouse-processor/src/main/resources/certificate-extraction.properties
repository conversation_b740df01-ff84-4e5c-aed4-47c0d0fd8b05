# 证书提取功能配置文件
# Certificate Extraction Configuration

# 是否启用证书提取功能
certificate.extraction.enabled=true

# 证书提取并行度
certificate.extraction.parallelism=2

# 证书相关Kafka Topic配置
kafka.topic.certificate.files=certfile
kafka.topic.system.certificates=certfile_system

# 证书分析器配置
certificate.analyzer.enabled=true
certificate.analyzer.parallelism=4
certificate.analyzer.buffer.size=1000
certificate.analyzer.timeout.ms=30000
certificate.analyzer.debug.enabled=false

# 示例配置说明：
# 1. certificate.extraction.enabled: 控制是否从SSL协议元数据中提取证书数据
# 2. certificate.extraction.parallelism: 证书提取算子的并行度
# 3. kafka.topic.certificate.files: 证书数据发送的Kafka topic名称
# 4. certificate.analyzer.*: 证书分析器相关配置
