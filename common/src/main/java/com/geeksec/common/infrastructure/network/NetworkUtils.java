package com.geeksec.common.infrastructure.network;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.*;
import java.util.regex.Pattern;

/**
 * 网络基础设施工具类
 *
 * 专注于网络基础概念：IP地址、端口、主机名、网络连通性等
 * 不依赖重型外部库，保持轻量级
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public final class NetworkUtils {

    private NetworkUtils() {
        // 工具类，禁止实例化
    }

    /** IPv4地址正则表达式 */
    private static final Pattern IPV4_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );

    /** IPv6地址正则表达式（简化版） */
    private static final Pattern IPV6_PATTERN = Pattern.compile(
        "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$"
    );

    /** IPv6地址分隔符 */
    private static final String IPV6_SEPARATOR = ":";



    /**
     * 检查字符串是否为有效的IPv4地址
     * 使用Java SDK的InetAddress进行验证，更加准确可靠
     *
     * @param ip IP地址字符串
     * @return 如果是有效的IPv4地址则返回true
     */
    public static boolean isValidIpv4(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }

        try {
            // 先用正则表达式快速过滤明显不符合格式的字符串
            if (!IPV4_PATTERN.matcher(ip).matches()) {
                return false;
            }

            // 使用InetAddress进行最终验证
            InetAddress addr = InetAddress.getByName(ip);
            return addr instanceof Inet4Address && ip.equals(addr.getHostAddress());
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为有效的IPv6地址
     * 使用Java SDK的InetAddress进行验证，支持各种IPv6格式
     *
     * @param ip IP地址字符串
     * @return 如果是有效的IPv6地址则返回true
     */
    public static boolean isValidIpv6(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }

        try {
            // 使用InetAddress进行验证，它能正确处理各种IPv6格式
            InetAddress addr = InetAddress.getByName(ip);
            return addr instanceof Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为有效的IP地址（IPv4或IPv6）
     * 这是统一的IP验证入口方法，优先使用Java SDK的InetAddress
     *
     * @param ip IP地址字符串
     * @return 如果是有效的IP地址则返回true
     */
    public static boolean isValidIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }

        try {
            // 直接使用InetAddress进行验证，它能处理IPv4和IPv6
            InetAddress addr = InetAddress.getByName(ip);
            // 确保输入的字符串确实是IP地址格式，而不是主机名
            return ip.equals(addr.getHostAddress()) ||
                   (addr instanceof Inet6Address && isValidIpv6Format(ip));
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检查IPv6地址格式是否正确（辅助方法）
     * 处理IPv6的各种表示形式，包括压缩格式
     *
     * @param ip IPv6地址字符串
     * @return 如果格式正确则返回true
     */
    private static boolean isValidIpv6Format(String ip) {
        // IPv6地址必须包含冒号
        if (!ip.contains(IPV6_SEPARATOR)) {
            return false;
        }

        // 检查是否包含无效字符
        return ip.matches("^[0-9a-fA-F:]+$");
    }





    /**
     * 检查IP地址是否为私有地址
     *
     * @param ip IP地址字符串
     * @return 如果是私有地址则返回true
     */
    public static boolean isPrivateIp(String ip) {
        if (!isValidIpv4(ip)) {
            return false;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr.isSiteLocalAddress();
        } catch (UnknownHostException e) {
            log.warn("解析IP地址失败: {}", ip, e);
            return false;
        }
    }



    /**
     * 检查IP地址是否为回环地址
     *
     * @param ip IP地址字符串
     * @return 如果是回环地址则返回true
     */
    public static boolean isLoopbackIp(String ip) {
        if (!isValidIp(ip)) {
            return false;
        }
        
        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr.isLoopbackAddress();
        } catch (UnknownHostException e) {
            log.warn("解析IP地址失败: {}", ip, e);
            return false;
        }
    }

    /**
     * 检查端口号是否有效
     *
     * @param port 端口号
     * @return 如果端口号有效则返回true
     */
    public static boolean isValidPort(int port) {
        return port >= 1 && port <= 65535;
    }

    /**
     * 检查端口号字符串是否有效
     *
     * @param portStr 端口号字符串
     * @return 如果端口号有效则返回true
     */
    public static boolean isValidPort(String portStr) {
        if (StringUtils.isEmpty(portStr)) {
            return false;
        }
        try {
            int port = Integer.parseInt(portStr);
            return isValidPort(port);
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 获取本机IP地址
     *
     * @return 本机IP地址，获取失败返回null
     */
    public static String getLocalIp() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("获取本机IP地址失败", e);
            return null;
        }
    }

    /**
     * 获取本机主机名
     *
     * @return 本机主机名，获取失败返回null
     */
    public static String getLocalHostName() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostName();
        } catch (UnknownHostException e) {
            log.warn("获取本机主机名失败", e);
            return null;
        }
    }

    /**
     * 检查主机是否可达
     *
     * @param host 主机地址
     * @param timeout 超时时间（毫秒）
     * @return 如果可达则返回true
     */
    public static boolean isReachable(String host, int timeout) {
        if (StringUtils.isEmpty(host)) {
            return false;
        }
        
        try {
            InetAddress addr = InetAddress.getByName(host);
            return addr.isReachable(timeout);
        } catch (Exception e) {
            log.warn("检查主机可达性失败: {}", host, e);
            return false;
        }
    }

    /**
     * 解析主机名到IP地址
     *
     * @param hostname 主机名
     * @return IP地址，解析失败返回null
     */
    public static String resolveHostname(String hostname) {
        if (StringUtils.isEmpty(hostname)) {
            return null;
        }
        
        try {
            InetAddress addr = InetAddress.getByName(hostname);
            return addr.getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("解析主机名失败: {}", hostname, e);
            return null;
        }
    }

    /**
     * 反向解析IP地址到主机名
     *
     * @param ip IP地址
     * @return 主机名，解析失败返回null
     */
    public static String reverseResolve(String ip) {
        if (!isValidIp(ip)) {
            return null;
        }
        
        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr.getHostName();
        } catch (UnknownHostException e) {
            log.warn("反向解析IP地址失败: {}", ip, e);
            return null;
        }
    }

    /**
     * 检查URL是否有效
     *
     * @param url URL字符串
     * @return 如果URL有效则返回true
     */
    public static boolean isValidUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }
        
        try {
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }

    /**
     * 从URL中提取主机名
     *
     * @param url URL字符串
     * @return 主机名，提取失败返回null
     */
    public static String extractHostFromUrl(String url) {
        if (!isValidUrl(url)) {
            return null;
        }
        
        try {
            URL urlObj = new URL(url);
            return urlObj.getHost();
        } catch (MalformedURLException e) {
            log.warn("从URL提取主机名失败: {}", url, e);
            return null;
        }
    }

    /**
     * 从URL中提取端口号
     *
     * @param url URL字符串
     * @return 端口号，如果URL中没有指定端口则返回-1，提取失败返回-1
     */
    public static int extractPortFromUrl(String url) {
        if (!isValidUrl(url)) {
            return -1;
        }
        
        try {
            URL urlObj = new URL(url);
            return urlObj.getPort();
        } catch (MalformedURLException e) {
            log.warn("从URL提取端口号失败: {}", url, e);
            return -1;
        }
    }
}
